import asyncio
import threading
from flask import Request, render_template
import requests

from .registry import Registry
from .utils import get_logger

logger = get_logger()
SVREGISTRY = Registry()

class ModelService:
    def __init__(self, config, upload_dir=None):
        self.input = config.get('input', {})
        self.output = config.get('output', {})
        self.parameters = config.get('parameters', {})
        self.upload_dir = upload_dir
        self.info = dict()
        self.config = config
        self.group = config.get('group', 'AI')
        self.type = config.get('type', 'Json')
        self.subtype = config.get('subtype', 'Foundry')
        self.description = config.get('description', 'No description provided')
        self.name = config.get('name', 'DefaultService')
        if 'url' in config.keys():
            self.url =  '/{}{}'.format(self.name, config.get('url', '/'))
        else:
            self.url = '/{}/predict'.format(self.name)

        # 注册到代理Clent后，该字段会被赋值
        self.proxy_url_path =""

    @staticmethod
    def create_service(config, upload_dir=None):
        service_name = config['name']

        service = SVREGISTRY[service_name](config, upload_dir)

        return service, service_name

    def homepage(self):
        default_type = ["image", "video", "audio", "text"]
        flag = 0
        for k,v in self.input.items():
            if v in default_type:
                self.input_type = v
                flag += 1
                break

        for k,v in self.output.items():
            if v in default_type:
                self.output_type = v
                flag += 1
                break
        if flag < 2:
            logger.error(" {} input or output type is not supported in default html".format(self.name))
            return " {} input or output type is not supported in default html".format(self.name)
        
        return render_template('base_index.html',
                               service_name=self.config['name'],
                               input_type=self.input_type,
                               output_type=self.output_type,
                               url=self.url)

    # @abstractmethod
    def predict(self, request: Request) -> dict:
        """
        对输入数据执行推理或处理
        :param input_data: 请求负载(字典格式)
        :return: 包含输出结果的字典
        """
        pass

    # @abstractmethod
    def health_check(self):
        """
        检查服务的健康状态
        :return: 包含健康状态信息的字典
        """
        return {"status": "ok"}
    # @abstractmethod

    # TODO
    def metadata(self) -> dict:
        """
        返回服务的元数据
        :return: 包含元数据信息的字典
        """
        # return {"result": list(self.get_urls().values())}
        pass

    # @abstractmethod
    # TODO
    def demo(self) -> str:
        """
        返回服务的演示页面或URL
        :return: 包含演示页面或URL的字符串
        """
        # render_template("demo.html")
        pass

    def get_service_type(self):

        """
        获取服务类型
        :return: 服务类型字符串
        """
        return ".".join([self.type, self.group, self.name])
    
    def get_api_type(self):
        """
        获取API类型
        :return: API类型字符串
        """
        service_group = self.group.lower()
        if "ai" in service_group:
            return "AI"
        elif "api" in service_group:
            return "API"
        elif "alg" in service_group:
            return "ALG"
        else:
            return "AI"


