<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent基本状态</title>
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --background-color: #f8f9fa;
            --text-color: #333;
            --border-color: #e0e0e0;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* 卡片容器 */
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 1000px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

            .card:hover {
                transform: translateY(-5px);
            }

        /* 卡片头部 */
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

            .card-header h1 {
                font-size: 24px;
                font-weight: 500;
                margin-bottom: 5px;
            }

            .card-header p {
                font-size: 14px;
                opacity: 0.9;
            }

        /* 卡片内容 */
        .card-body {
            padding: 20px;
        }

        /* 信息展示区域左右布局 */
        .info-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-group {
            flex: 1;
            min-width: 45%;
            margin-bottom: 10px;
            position: relative;
        }

        .info-label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .info-value {
            display: flex;
            align-items: center;
            font-size: 16px;
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

            .info-value span {
                flex-grow: 1;
                overflow: hidden;
                text-overflow: ellipsis;
            }

        /* 图标样式 */
        .icon {
            margin-right: 10px;
            color: var(--primary-color);
        }

        /* 复制按钮 */
        .copy-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            padding: 5px;
            transition: color 0.2s;
        }

            .copy-btn:hover {
                color: var(--secondary-color);
            }

        /* 状态指示器 */
        .status {
            display: inline-flex;
            align-items: center;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--danger-color);
        }

        .status-warning {
            background-color: var(--warning-color);
        }

        /* 表格样式 */
        .table-container {
            margin-top: 30px;
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

            .data-table th,
            .data-table td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid var(--border-color);
            }

            .data-table th {
                background-color: #f5f7fa;
                font-weight: 500;
                color: #666;
                font-size: 14px;
            }

            .data-table tr:last-child td {
                border-bottom: none;
            }

            .data-table tr:hover {
                background-color: #f9fafb;
            }

            /* 表格链接样式优化 */
            .data-table a {
                color: inherit;
                text-decoration: none;
                position: relative;
                padding: 2px 0;
            }

                .data-table a::after {
                    content: '';
                    position: absolute;
                    width: 0;
                    height: 1px;
                    bottom: 0;
                    left: 0;
                    background-color: var(--primary-color);
                    transition: width 0.2s ease;
                }

            .data-table tr:hover a::after {
                width: 100%;
            }

            .data-table a:hover {
                color: var(--primary-color);
            }

        .table-caption {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
            color: #333;
        }

        /* 响应式调整 */
        @media (max-width: 600px) {
            .card {
                max-width: 100%;
            }

            .card-header h1 {
                font-size: 20px;
            }

            .info-value {
                font-size: 14px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 10px;
                font-size: 13px;
            }

            .info-group {
                min-width: 100%;
            }
        }

        /* 加载状态 */
        .loading {
            color: #666;
            font-style: italic;
        }

        /* 工具提示 */
        .tooltip {
            position: absolute;
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            top: -30px;
            right: 0;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }

            .tooltip.show {
                opacity: 1;
            }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <h1>Agent基本状态</h1>
        </div>
        <div class="card-body">
            <!-- 现有信息项 - 左右布局 -->
            <div class="info-container">
                <div class="info-group">
                    <label class="info-label">节点ID</label>
                    <div class="info-value">
                        <span id="nodeId" class="loading">加载中...</span>
                        <button class="copy-btn" data-target="nodeId" title="复制">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 3H8C6.34315 3 5 4.34315 5 6V20C5 21.6569 6.34315 23 8 23H16C17.6569 23 19 21.6569 19 20V6C19 4.34315 17.6569 3 16 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M16 3V7C16 8.65685 14.6569 10 13 10H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M8 14H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                                <path d="M8 18H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                            </svg>
                            <span class="tooltip">已复制</span>
                        </button>
                    </div>
                </div>

                <div class="info-group">
                    <label class="info-label">节点名称</label>
                    <div class="info-value">
                        <span id="nodeName" class="loading">加载中...</span>
                        <button class="copy-btn" data-target="nodeName" title="复制">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 3H8C6.34315 3 5 4.34315 5 6V20C5 21.6569 6.34315 23 8 23H16C17.6569 23 19 21.6569 19 20V6C19 4.34315 17.6569 3 16 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M16 3V7C16 8.65685 14.6569 10 13 10H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M8 14H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                                <path d="M8 18H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                            </svg>
                            <span class="tooltip">已复制</span>
                        </button>
                    </div>
                </div>

                <div class="info-group">
                    <label class="info-label">节点状态</label>
                    <div class="info-value">
                        <div id="nodeStatus" class="status loading">加载中...</div>
                    </div>
                </div>

                <div class="info-group">
                    <label class="info-label">IP地址</label>
                    <div class="info-value">
                        <span id="nodeIp" class="loading">加载中...</span>
                        <button class="copy-btn" data-target="nodeIp" title="复制">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 3H8C6.34315 3 5 4.34315 5 6V20C5 21.6569 6.34315 23 8 23H16C17.6569 23 19 21.6569 19 20V6C19 4.34315 17.6569 3 16 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M16 3V7C16 8.65685 14.6569 10 13 10H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M8 14H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                                <path d="M8 18H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                            </svg>
                            <span class="tooltip">已复制</span>
                        </button>
                    </div>
                </div>

                <!-- 新增：主节点地址 -->
                <div class="info-group">
                    <label class="info-label">主节点地址</label>
                    <div class="info-value">
                        <span id="mainNodeAddress" class="loading">加载中...</span>
                        <button class="copy-btn" data-target="mainNodeAddress" title="复制">
                            <svg class="icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 3H8C6.34315 3 5 4.34315 5 6V20C5 21.6569 6.34315 23 8 23H16C17.6569 23 19 21.6569 19 20V6C19 4.34315 17.6569 3 16 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M16 3V7C16 8.65685 14.6569 10 13 10H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M8 14H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                                <path d="M8 18H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                            </svg>
                            <span class="tooltip">已复制</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 新增：表格 -->
            <div class="table-container">
                <div class="table-caption">服务列表</div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>描述</th>
                            <th>状态</th>
                            <th>服务地址</th>
                            <th>服务前缀</th>
                            <th>端口号</th>
                        </tr>
                    </thead>
                    <tbody id="itemsTableBody">
                        <tr>
                            <td colspan="6" class="loading" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        window.node = {{$params}};
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function () {
            // 检查window.node是否存在
            if (typeof window.node === 'undefined') {
                // 模拟数据用于测试
                window.node = {
                    id: 'node-123456',
                    name: '测试节点',
                    status: 'online',
                    ip: '*************',
                    mainNodeAddress: '***********:8080',
                    items: [
                        {
                            name: '服务A',
                            description: '主要业务服务',
                            status: 'online',
                            servicePath: '/api/serviceA',
                            serviceBaseUri: '/api/v1',
                            port: 8080
                        },
                        {
                            name: '服务B',
                            description: '数据处理服务',
                            status: 'warning',
                            servicePath: '/api/serviceB',
                            serviceBaseUri: '/api/v2',
                            port: 8081
                        },
                        {
                            name: '服务C',
                            description: '认证授权服务',
                            status: 'offline',
                            servicePath: '/api/auth',
                            serviceBaseUri: '/api',
                            port: 8082
                        }
                    ]
                };
            }

            // 填充基本信息
            document.getElementById('nodeId').textContent = window.node.nodeid || '未知';
            document.getElementById('nodeName').textContent = window.node.hostname || '未知';
            document.getElementById('nodeIp').textContent = window.node.ip || '未知';
            document.getElementById('mainNodeAddress').textContent = window.node.mainNodeAddress || '未知';

            // 处理状态显示
            const statusElement = document.getElementById('nodeStatus');
            const status = window.node.status || 'unknown';

            if (status === 'online') {
                statusElement.innerHTML = '<span class="status-indicator status-online"></span>在线';
            } else if (status === 'offline') {
                statusElement.innerHTML = '<span class="status-indicator status-offline"></span>离线';
            } else if (status === 'warning') {
                statusElement.innerHTML = '<span class="status-indicator status-warning"></span>警告';
            } else {
                statusElement.textContent = '未知状态';
            }

            // 填充表格数据
            const tableBody = document.getElementById('itemsTableBody');
            const items = window.node.items || [];

            if (items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center;">无数据</td></tr>';
            } else {
                tableBody.innerHTML = '';
				var uri = window.location.pathname;
				if (uri && uri[uri.length - 1] === '/') {
					uri=uri.slice(0, -1);
				}
                items.forEach(item => {
                    const row = document.createElement('tr');

                    // 处理状态样式
                    let statusClass = 'status-offline';
                    let statusText = '离线';

                    if (item.status === 'Running') {
                        statusClass = 'status-online';
                        statusText = '在线';
                    } else if (item.status === 'Active') {
                        statusClass = 'status-warning';
                        statusText = '激活';
                    }

                    // 保持链接功能但优化样式
					
                    row.innerHTML = `
                            <td><a href="${uri}/ClientService/${encodeURIComponent(item.name || '')}">${item.name || '-'}</a></td>
                            <td>${item.description || '-'}</td>
                            <td><div class="status"><span class="status-indicator ${statusClass}"></span>${statusText}</div></td>
                            <td><a href="${item.servicePath || '#'}" target="_blank">${item.servicePath || '-'}</a></td>
                            <td>${item.serviceBaseUri || '-'}</td>
                            <td>${item.port || '-'}</td>
                        `;

                    tableBody.appendChild(row);
                });
            }

            // 移除所有加载状态类
            document.querySelectorAll('.loading').forEach(el => {
                el.classList.remove('loading');
            });

            // 复制按钮功能
            document.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', function () {
                    const targetId = this.getAttribute('data-target');
                    const targetElement = document.getElementById(targetId);
                    const textToCopy = targetElement.textContent;

                    navigator.clipboard.writeText(textToCopy).then(() => {
                        // 显示提示
                        const tooltip = this.querySelector('.tooltip');
                        tooltip.classList.add('show');
                        setTimeout(() => {
                            tooltip.classList.remove('show');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败: ', err);
                    });
                });
            });
        });
    </script>
</body>
</html>