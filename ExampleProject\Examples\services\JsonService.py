from flask import jsonify
from BaseOS.ai.framework import ModelService,SVREGISTRY
from BaseOS.ai.framework.utils import get_logger
import json

logger = get_logger()

@SVREGISTRY.register_module("JsonService")
class JsonService(ModelService):
    def __init__(self, config,upload_dir):
        """
        初始化服务对象
        :param config: server配置字典对象
        """
        super().__init__(config,upload_dir)

    def predict(self, request):
        try:
            # 获取输入的JSON数据
            if request.is_json:
                # 直接从请求体获取JSON数据
                json_data = request.get_json()
            else:
                # 如果是表单提交的JSON字符串
                json_data = request.form.get('json')
                if not json_data:
                    return jsonify({'error': 'No JSON data provided'}), 400
                
                # 尝试解析JSON字符串
                try:
                    if isinstance(json_data, str):
                        json_data = json.loads(json_data)
                except json.JSONDecodeError:
                    return jsonify({'error': 'Invalid JSON format'}), 400

            # 直接返回相同的JSON数据
            return json_data
            #return jsonify(json_data)

        except Exception as e:
            logger.error(f"Error processing JSON: {str(e)}")
            return jsonify({'error': str(e)}), 500




