[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[bdist_wheel]
universal = 1

[project]
name = "BaseOS-AI-Framework"
version = "0.1.0"
description = "AI Service Framework"
requires-python = ">=3.7"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
]
readme = "README.md"

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
"BaseOS.ai.proxy" = ["BaseOS.Desktop.TunnelGateway", "BaseOS.Desktop.TunnelGateway.exe"]