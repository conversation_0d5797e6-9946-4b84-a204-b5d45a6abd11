<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ service_name }} Demo</title>
    <style>
        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
        }
        .upload-section, .predict-section, .result-section {
            flex: 1;
            border: 2px dashed #ccc;
            padding: 20px;
            min-height: 400px;
            text-align: center;
        }
        .preview-content, .result-content {
            max-width: 100%;
            max-height: 400px;
            margin-top: 20px;
        }
        button {
            padding: 15px 30px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .info-panel {
            padding: 20px;
            background: #f5f5f5;
            margin-top: 20px;
        }
        .upload-btn {
            margin: 20px 0;
        }
        h2 {
            margin-bottom: 20px;
        }
        textarea {
            width: 90%;
            height: 300px;
            margin: 10px;
            padding: 10px;
        }
        audio {
            margin-top: 20px;
        }
        .preview-btn {
            padding: 8px 15px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin: 10px 0;
            display: block;
            margin: 10px auto;
        }

        .preview-btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧上传区域 -->
        <div class="upload-section">
            <h2>输入</h2>
            <form id="uploadForm" action="/predict" method="post" enctype="multipart/form-data">
                <div id="inputContainer">
                    <!-- 输入控件将根据input_type动态插入 -->
                </div>
            </form>
            <div id="previewContainer" class="preview-content">
                <!-- 预览内容将根据input_type动态显示 -->
            </div>
        </div>

        <!-- 中间predict按钮区域 -->
        <div class="predict-section">
            <h2>操作</h2>
            <button type="submit" form="uploadForm" class="predict-btn">开始处理</button>
        </div>

        <!-- 右侧结果区域 -->
        <div class="result-section">
            <h2>输出</h2>
            <div id="resultContainer" class="result-content">
                <!-- 结果内容将根据output_type动态显示 -->
            </div>
        </div>
    </div>

    <script>
        const INPUT_TYPE = "{{ input_type }}";
        const OUTPUT_TYPE = "{{ output_type }}";
        const SERVICE_NAME = "{{ service_name }}";
        const PREDICT_URL = "{{ url }}"; // 修改变量名

        // 初始化输入控件
        function initializeInput() {
            const inputContainer = document.getElementById('inputContainer');
            switch(INPUT_TYPE) {
                case 'image':
                    inputContainer.innerHTML = `
                        <input type="file" name="file" accept="image/*" required>
                    `;
                    break;
                case 'audio':
                    inputContainer.innerHTML = `
                        <input type="file" name="file" accept="audio/*" required>
                    `;
                    break;
                case 'text':
                    inputContainer.innerHTML = `
                        <textarea name="text" required placeholder="请输入文本..."></textarea>
                        <button type="button" class="preview-btn">预览</button>
                    `;
                    break;
                case 'json':
                    inputContainer.innerHTML = `
                        <textarea name="json" required placeholder="请输入JSON数据..."></textarea>
                        <button type="button" class="preview-btn">预览</button>
                    `;
                    break;
            }
        }

        // 处理预览
        function handlePreview(file) {

            
            const previewContainer = document.getElementById('previewContainer');
            
            if (INPUT_TYPE === 'image') {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewContainer.innerHTML = `<img src="${e.target.result}" class="preview-content">`;
                };
                reader.readAsDataURL(file);
            } else if (INPUT_TYPE === 'audio') {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewContainer.innerHTML = `<audio controls src="${e.target.result}"></audio>`;
                };
                reader.readAsDataURL(file);
            } else if (INPUT_TYPE === 'text' || INPUT_TYPE === 'json') {
                previewContainer.innerHTML = `<pre>${file}</pre>`;
            }
        }

        // 处理结果显示
        function displayResult(data) {
            const resultContainer = document.getElementById('resultContainer');
            
            switch(OUTPUT_TYPE) {
                case 'image':
                    const imageUrl = URL.createObjectURL(data);
                    resultContainer.innerHTML = `<img src="${imageUrl}" class="result-content">`;
                    break;
                case 'audio':
                    const audioUrl = URL.createObjectURL(data);
                    resultContainer.innerHTML = `<audio controls src="${audioUrl}"></audio>`;
                    break;
                case 'text':
                    data.text().then(text => {
                        resultContainer.innerHTML = `<pre>${text}</pre>`;
                    });
                    break;
                case 'json':
                    data.json().then(json => {
                        resultContainer.innerHTML = `<pre>${JSON.stringify(json, null, 2)}</pre>`;
                    });
                    break;
            }
        }

        // 初始化表单处理
        function initializeForm() {
            const form = document.getElementById('uploadForm');
            const input = form.querySelector('input[type="file"], textarea');
            const previewBtn = form.querySelector('.preview-btn');

            // 处理文件类型的预览
            if (input && (INPUT_TYPE === 'image' || INPUT_TYPE === 'audio')) {
                input.addEventListener('change', (e) => {
                    handlePreview(e.target.files[0]);
                });
            }

            // 处理 text/json 的预览按钮点击
            if (previewBtn && (INPUT_TYPE === 'text' || INPUT_TYPE === 'json')) {
                previewBtn.addEventListener('click', () => {
                    const content = input.value;
                    if (INPUT_TYPE === 'json') {
                        try {
                            // 尝试解析 JSON 并格式化显示
                            const jsonObj = JSON.parse(content);
                            document.getElementById('previewContainer').innerHTML = 
                                `<pre>${JSON.stringify(jsonObj, null, 2)}</pre>`;
                        } catch (e) {
                            alert('Invalid JSON format');
                        }
                    } else {
                        // 文本直接显示
                        document.getElementById('previewContainer').innerHTML = 
                            `<pre>${content}</pre>`;
                    }
                });
            }

            // 处理表单提交
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(form);
                
                try {
                    let response;
                    response = await fetch(PREDICT_URL, {
                            method: 'POST',
                            body: formData
                        });
                    
                    if (!response.ok) throw new Error('Network response was not ok');
                    
                    if (OUTPUT_TYPE === 'image' || OUTPUT_TYPE === 'audio') {
                        const blob = await response.blob();
                        displayResult(blob);
                    } else if (OUTPUT_TYPE === 'json') {
                        displayResult(response);
                    } else {
                        displayResult(response);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('处理失败，请重试');
                }
            });
        }

        // 初始化页面
        window.addEventListener('load', () => {
            initializeInput();
            initializeForm();
        });
    </script>
</body>
</html>