import os
from BaseOS.ai.framework import Server
from services import *

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_PATH = os.path.join(BASE_DIR, 'config.json')
UPLOAD_DIR = os.path.join(BASE_DIR, 'services', 'uploads')
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

AServer = Server(CONFIG_PATH,UPLOAD_DIR)
AServer.create_services_from_config()

app = AServer.app
app.static_folder = os.path.join(BASE_DIR, 'static')

AServer.bind_sv_urls()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
