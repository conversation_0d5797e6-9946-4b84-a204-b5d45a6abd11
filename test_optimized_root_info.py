#!/usr/bin/env python3
"""
测试优化后的根路径info页面实现
验证基本信息只计算一次，服务列表信息每次重新获取
"""
import os
import sys
import json
from unittest.mock import patch, MagicMock

# 添加项目路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ai_service_framework', 'src'))

def test_optimized_root_info():
    """测试优化后的根路径info页面功能"""
    
    # 模拟环境变量
    with patch.dict(os.environ, {
        'BASEOSNODEID': 'optimized-test-node-456',
        'HOST_IP': '*************',
        'SERVER_PORT': '5000'
    }):
        from BaseOS.ai.framework import Server
        
        # 创建测试配置文件
        test_config = {
            "app": {
                "name": "optimized-test-server",
                "name_zh": "优化测试服务器",
                "host": "0.0.0.0",
                "port": 5000,
                "version": "1.0.0",
                "description": "An optimized test Flask server application"
            },
            "register": {
                "proxy_client_name": "OptimizedTestClient",
                "proxy_client_group": "OptimizedTestGroup",
                "proxy_client_port": 8090,
                "proxy_server_ip": "*************",
                "proxy_server_port": 8090
            },
            "services": {
                "Service1": {
                    "name_zh": "服务1",
                    "name": "Service1",
                    "url": "/predict1",
                    "type": "Json",
                    "subtype": "Demo",
                    "group": "BaseOS.API.Foundry"
                },
                "Service2": {
                    "name_zh": "服务2",
                    "name": "Service2",
                    "url": "/predict2",
                    "type": "Text",
                    "subtype": "NLP",
                    "group": "BaseOS.AI.Foundry"
                }
            }
        }
        
        # 保存测试配置文件
        config_path = 'optimized_test_config.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        try:
            # 模拟ModelService.create_service方法
            mock_service1 = MagicMock()
            mock_service1.name = "Service1"
            mock_service1.name_zh = "服务1"
            mock_service1.url = "/Service1/predict1"
            
            mock_service2 = MagicMock()
            mock_service2.name = "Service2"
            mock_service2.name_zh = "服务2"
            mock_service2.url = "/Service2/predict2"
            
            with patch('BaseOS.ai.framework.server.ModelService.create_service') as mock_create:
                # 设置create_service的返回值
                mock_create.side_effect = [
                    (mock_service1, "Service1"),
                    (mock_service2, "Service2")
                ]
                
                # 模拟代理请求
                with patch('requests.post') as mock_post:
                    mock_response = MagicMock()
                    mock_response.status_code = 200
                    mock_response.json.return_value = {"url_path": "/optimized-proxy-path"}
                    mock_post.return_value = mock_response
                    
                    # 创建Server实例
                    server = Server(config_path, upload_dir='./optimized_test_uploads')
                    
                    print("✓ Server实例创建成功")
                    
                    # 验证基本数据已初始化
                    assert hasattr(server, '_root_info_base_data'), "基本数据未初始化"
                    base_data = server._root_info_base_data
                    
                    print("✓ 基本数据初始化成功:")
                    print(f"  - 节点ID: {base_data['nodeid']}")
                    print(f"  - 主机名: {base_data['hostname']}")
                    print(f"  - 状态: {base_data['status']}")
                    print(f"  - IP地址: {base_data['ip']}")
                    print(f"  - 主节点地址: {base_data['mainNodeAddress']}")
                    
                    # 验证代理地址是否正确拼接
                    expected_main_address = "http://*************:8888/optimized-proxy-path"
                    assert base_data['mainNodeAddress'] == expected_main_address, \
                        f"主节点地址不正确: {base_data['mainNodeAddress']}"
                    
                    print("✓ 代理地址拼接正确")
                    
                    # 创建服务
                    server.create_services_from_config()
                    
                    print("✓ 服务创建成功")
                    print(f"  - 服务数量: {len(server.service_info_list)}")
                    
                    # 测试多次调用get_root_info方法
                    with server.app.app_context():
                        print("✓ 测试多次调用get_root_info方法...")
                        
                        # 第一次调用
                        result1 = server.get_root_info()
                        print("  - 第一次调用成功")
                        
                        # 修改服务列表（模拟服务状态变化）
                        original_service_list = server.service_info_list.copy()
                        server.service_info_list.append({
                            'name': 'Service3',
                            'name_zh': '动态添加的服务3',
                            'url': '/Service3/predict3',
                            'status': 'online'
                        })
                        
                        # 第二次调用
                        result2 = server.get_root_info()
                        print("  - 第二次调用成功（服务列表已更新）")
                        
                        # 验证基本信息没有重新计算（通过检查代理请求次数）
                        # mock_post应该只被调用一次（在初始化时）
                        assert mock_post.call_count == 1, \
                            f"代理请求被调用了{mock_post.call_count}次，应该只调用1次"
                        
                        print("✓ 基本信息只计算了一次（代理请求只调用1次）")
                        
                        # 验证服务列表信息确实更新了
                        # 解析第二次调用的结果，检查是否包含新添加的服务
                        # 这里简单检查结果长度是否不同
                        assert len(result2) > len(result1), "服务列表信息未正确更新"
                        
                        print("✓ 服务列表信息正确更新")
                        
                        # 恢复原始服务列表
                        server.service_info_list = original_service_list
                        
                        # 第三次调用
                        result3 = server.get_root_info()
                        print("  - 第三次调用成功（服务列表已恢复）")
                        
                        # 验证代理请求仍然只调用了一次
                        assert mock_post.call_count == 1, \
                            f"代理请求被调用了{mock_post.call_count}次，应该只调用1次"
                        
                        print("✓ 多次调用验证完成，基本信息确实只计算了一次")
                        
        finally:
            # 清理测试文件
            for file_path in [config_path]:
                if os.path.exists(file_path):
                    os.remove(file_path)
            if os.path.exists('./optimized_test_uploads'):
                import shutil
                shutil.rmtree('./optimized_test_uploads', ignore_errors=True)

if __name__ == '__main__':
    print("开始测试优化后的根路径info页面功能...")
    print("=" * 60)
    try:
        test_optimized_root_info()
        print("\n🎉 所有测试通过！")
        print("\n📊 优化效果:")
        print("✅ 基本信息（节点ID、名称、IP、主节点地址）只在初始化时计算一次")
        print("✅ 服务列表信息每次调用时重新获取，确保数据实时性")
        print("✅ 避免了重复的代理请求，提高了性能")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
