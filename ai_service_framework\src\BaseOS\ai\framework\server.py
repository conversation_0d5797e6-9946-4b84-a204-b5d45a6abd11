import os
import importlib
import json
import uuid
import time
from datetime import datetime, timezone
from abc import ABC
from functools import partial

from flask import Flask, Response, g, request
import requests

from .service import ModelService
from .utils import get_logger


logger = get_logger()


def load_config(path: str) -> ModelService:

    module_name, class_name = path.rsplit('.', 1)
    module = importlib.import_module(module_name)
    cls = getattr(module, class_name)
    if not issubclass(cls, ModelService):
        raise ImportError("{} is not a subclass of ModelService".format(path))
    return cls


class Server(ABC):
    """
    提供server的各种方法
    """

    def __init__(self, config=None, upload_dir=None):
        with open(config, 'r', encoding='utf-8') as file:
            self.config = json.load(file)
        self.services = dict()
        self.upload_dir = upload_dir
        self.port = self.config["app"]["port"]
        self.name = self.config["app"]["name"]
        self.register = self.config.get("register", None)
        self._proxy_process = None
        self.get_url_dict()
        if self.register:
            self.start_proxy_process()
            time.sleep(10)  # 等待代理进程启动

        # 从环境变量中获取这些值，用来注册给agent的
        self.host_ip = os.environ.get("HOST_IP", "127.0.0.1")
        self.server_port = os.environ.get("SERVER_PORT", 5000)
        self.baseos_node_id = os.environ.get("BASEOSNODEID", "default_baseos_id")
        self.create_app()
        self.heartbeat_interval = 60
        self._heartbeat_task = None
    
    def get_url_dict(self):
        """
        获取服务的URL字典
        :return: 包含服务名称和对应URL的字典
        """
        self.url2service = dict()
        for service_name, svconfig in self.config["services"].items():
            svurl = svconfig.get("url", '/{}/predict'.format(service_name))
            self.url2service[svurl] = service_name

    def create_app(self) -> Flask:
        """
        Factory to create Flask app with injected service implementation.
        """
        template_path = os.path.join(os.path.dirname(__file__), 'templates')
        app = Flask(__name__, template_folder=template_path)

        @app.before_request
        def inject_meta():
            """
            为每个请求注入 CloudEvents 风格的元信息到 flask.g.meta, 并封装原始请求数据
            """
            # 获取原始请求数据（JSON）以便后续使用
            raw_data = None
            try:
                raw_data = request.get_json(force=True)
            except Exception:
                raw_data = None

            path_parts = request.path.split(os.sep)
            service = None
            
            if path_parts[1] == 'info':
                type_info = 'info'

            elif path_parts[1] in self.config["services"].keys():
                service = path_parts[1]
                info = self.config["services"][service]
                type_info = '.'.join([info["type"],info["subtype"],info["name"]])

            else:
                service = self.url2service.get(request.path, None)
                if service is None:
                    logger.error("Service not found for path: {}".format(request.path))
                    return Response(
                        json.dumps({"error": "Service not found"}),
                        status=404
                    )
            
            g.meta = {
                # req: 唯一事件ID
                "id": str(uuid.uuid4()),
                # opt: 版本
                "specversion": "1.0",
                # req: 事件类型
                "type": type_info,
                # req: 事件来源
                "source": request.path,
                # opt: 事件来源详情，可根据需要自定义
                "subject": request.headers.get("X-Subject", ""),
                # opt: 请求时间戳
                "time": datetime.now(timezone.utc).isoformat(),
                # req: 数据内容类型
                "datacontenttype": request.content_type or "application/json",
                # req: 原始请求数据
                "data": raw_data
            }

        # @app.after_request
        # def wrap_response(response: Response) -> Response:
        #     """
        #     全局响应包装，将原始 JSON 响应体封装到标准事件格式中
        #     """
        #     # 仅处理 JSON 响应
        #     if response.content_type == 'application/json' and response.status_code < 500:
        #         try:
        #             payload = json.loads(response.get_data())
        #         except Exception:
        #             payload = None

        #         envelope = {
        #             **g.meta,
        #             "data": payload if isinstance(payload, list) else [payload],
        #             # 以下内容后续开发，增加错误信息 2025年4月23日
        #             "errorCode": 0,
        #             "errorMessage": ""
        #         }
        #         response.set_data(json.dumps(envelope))
        #         response.mimetype = 'json'
        #     return response
        
        info_view = partial(self.get_info)
        info_view.__name__ = 'info'
        app.add_url_rule('/info', view_func=info_view, methods=['GET'])

        self.app = app
    
    def get_info(self):
            """
            获取服务的基本信息
            :return: 包含服务名称、端口和URL的字典
            """
            sv2url = dict()
            for service_name, svconfig in self.config["services"].items():  # 遍历服务配置
                url = svconfig.get("url", f'/{service_name}/predict')
                sv2url[service_name] = url
            return sv2url
    
    def get_info(self):
            """
            获取服务的基本信息
            :return: 包含服务名称、端口和URL的字典
            """
            sv2url = dict()
            for service_name, svconfig in self.config["services"].items():  # 遍历服务配置
                url = svconfig.get("url", f'/{service_name}/predict')
                sv2url[service_name] = url
            return sv2url

    def create_services_from_config(self):
        self.name_to_service = dict()
        self.service_to_name = dict()

        assert ('services' in self.config)

        assert (isinstance(self.config['services'], dict))

        for k, v in self.config["services"].items():
            sv, svname = ModelService.create_service(v, self.upload_dir)
            self.name_to_service[svname] = sv
            self.service_to_name[sv] = svname

    def bind_sv_urls(self):
        routers = []
        for k, v in self.config["services"].items():
            svname = v["name"]
            sv = self.name_to_service[svname]
            url_prefix = '/{}'.format(svname)

            # 使用 partial 包装实例方法
            predict_view = partial(sv.predict, request)
            health_view = partial(sv.health_check)
            demo_view = partial(sv.demo)
            metadata_view = partial(sv.metadata)
            homepage_view = partial(sv.homepage)

            # 更新视图函数的 __name__ 属性
            predict_view.__name__ = svname + 'predict'
            health_view.__name__ = svname + 'health'
            demo_view.__name__ = svname + 'demo'
            metadata_view.__name__ = svname + 'metadata'
            homepage_view.__name__ = svname + 'home'

            # 注册路由
            self.app.add_url_rule('{}/healthcheck'.format(url_prefix), view_func=health_view, methods=['GET'])
            self.app.add_url_rule('{}/demo'.format(url_prefix), view_func=demo_view)
            self.app.add_url_rule('{}/metadata'.format(url_prefix), view_func=metadata_view, methods=['GET'])
            self.app.add_url_rule('{}/home'.format(url_prefix), view_func=homepage_view)
            # 处理预测请求，位置需要放到绑定路由的最后，因为支持通配符，用户可能会绑定成 / ，这样的话会导致其他路由无法访问
            self.app.add_url_rule(sv.url, view_func=predict_view, methods=['POST', 'GET'])
            self.app.add_url_rule('{}/<path:subpath>'.format(sv.url), view_func=predict_view, methods=['POST', 'GET'])

            logger.info("Service {} bound to URL {}".format(svname, sv.url))
            logger.info("Service {} bound to URL {}/healthcheck".format(svname, url_prefix))
            logger.info("Service {} bound to URL {}/demo".format(svname, url_prefix))
            logger.info("Service {} bound to URL {}/metadata".format(svname, url_prefix))
            logger.info("Service {} bound to URL {}/home".format(svname, url_prefix))
            # 在client中注册
            if self.register:
                client_url = "http://localhost:{0}/url/register".format(
                    self.register.get("proxy_client_port", 8090))
                self._register_2_client(sv, client_url, self.port)

            # 收集router信息
            if not sv.config.get("url") or not sv.proxy_url_path or not self._is_url_supported(sv.url):
                continue
            
            router_info = {
                "router": sv.config.get("url"),
                "type": sv.get_service_type(),
                "address": "{0}/{1}".format(sv.proxy_url_path, sv.name),#约定，不能修改，需要和sv.url的生成保持一致，都是以sv.name开头
                "models": sv.config.get("parameters", {}).get("models", "[]")
            }
            routers.append(router_info)

        # 只注册一次到agent
        if self.register:
            self._register_2_agent(
                routers,
                self.register.get("agent_register_url"),
                self.register.get("agent_heatbeat_url"),
                self.host_ip,
                self.server_port
            )

    def remove_file(filepath):
        try:
            os.remove(filepath)
            logger.info("file {} removed".format(filepath))
        except FileNotFoundError:
            logger.error("file {} not exits".format(filepath))
        except PermissionError:
            logger.error("{} permission denied".format(filepath))
        except Exception as e:
            logger.error("remove failed: {}".format(e))

    def start_proxy_process(self):
        """
        启动 TunnelGatewayClient 代理进程，参数从配置文件 register 字段读取
        """
        import platform
        import subprocess
        import threading
        from pathlib import Path

        proxy_client_name = self.register.get("proxy_client_name", "DefaultClient")
        proxy_client_port = self.register.get("proxy_client_port", 8090)
        proxy_server_ip = self.register.get("proxy_server_ip", "127.0.0.1")
        proxy_server_port = self.register.get("proxy_server_port", 8090)

        # 获取包安装路径
        package_path = Path(__file__).parent.parent
        proxy_path = package_path / "proxy"

        # 根据操作系统选择可执行文件
        system = platform.system().lower()
        if system == "windows":
            executable = proxy_path / "BaseOS.Desktop.TunnelGateway.exe"

        else:
            executable = proxy_path / "BaseOS.Desktop.TunnelGateway"

        os.chmod(executable, 0o755)
        if not executable.exists():
            logger.error("Proxy executable not found: {}".format(executable))
            return

        # 拼接命令行参数
        cmd = [
            str(executable),
            "-server", str(proxy_server_ip),
            "-port", str(proxy_server_port),
            "-host", "127.0.0.1",
            "-manager", str(proxy_client_port),
            "-type", "AI",
            "-name", str(proxy_client_name),
            "-group", str(self.register.get("proxy_client_group", "DefaultGroup"))
        ]

        def log_output(pipe, prefix):
            """读取进程输出并写入日志"""
            try:
                for line in iter(pipe.readline, ''):
                    if line:
                        # 移除行尾的换行符并记录到日志
                        logger.info("[{}] {}".format(prefix, line.rstrip()))
            except Exception as e:
                logger.error("Error reading {} output: {}".format(prefix, e))
            finally:
                pipe.close()

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )

            # 启动线程来处理stdout和stderr输出
            stdout_thread = threading.Thread(
                target=log_output,
                args=(process.stdout, "PROXY"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=log_output,
                args=(process.stderr, "PROXY"),
                daemon=True
            )

            stdout_thread.start()
            stderr_thread.start()

            logger.info(
                "Started proxy process with PID: {}, cmd: {}".format(process.pid, ' '.join(cmd)))
            self._proxy_process = process
        except Exception as e:
            logger.error("Failed to start proxy process: {}".format(e))

    def _register_2_client(self, sv, client_register_url, port):
        """
        将服务注册到本地Client
        """
        p = {
            "app_name": self.name,
            "service_address": str(port),
            "service_name": sv.name,
            "service_group": sv.group,
            "base_url": sv.url,
            "api_type": sv.get_api_type()
        }

        logger.info("Registering service {} to client at {} with params: {}".format(sv.name, client_register_url, p))
        try:
            response = requests.post(
                client_register_url,
                json=p,
                timeout=10)
            logger.info("Service {} registered to client at {}".format(sv.name, client_register_url))
            logger.info("Response: {} - {}".format(response.status_code, response.text))
            resp_json = response.json()
            sv.proxy_url_path = resp_json.get("url_path", "")
        except requests.exceptions.RequestException as e:
            logger.error("Failed to register service {} to client: {}".format(sv.name, str(e)))

    def _is_url_supported(self, url):
        """
        检查URL是否在支持的列表中
        """
        supported_urls = ["/embeddings", "/chat/completions", "/audio/transcriptions"]
        for v in supported_urls:
            if v in url:
                return True
        return False

    def _register_2_agent(self, routers, agent_register_url, agent_heatbeat_url, host_ip, server_port):
        """
        批量将服务注册到远程Agent
        """
        payload = {
            "serverName": self.register.get("proxy_client_name", "DefaultClientName") + "_" + self.name,  # agent唯一key
            "aliasName": self.name,
            "routers": routers,
            "groupName": self.register.get("proxy_client_group", "DefaultGroup"),
            "appName": self.name,
            "clientName": self.register.get("proxy_client_name", "DefaultClientName")
        }
        logger.info("request agent register body: {}".format(payload))

        logger.info("service ip: {}, port: {}".format(host_ip, server_port))
        if not agent_register_url or not agent_heatbeat_url:
            logger.warning("Agent registration URLs are not provided, skipping registration.")
            return
        if not routers:
            logger.error("No routers to register to Agent.")
            return
        
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(agent_register_url, json=payload, headers=headers)
            if response.status_code == 200:
                logger.info("Services registered successfully")
            else:
                logger.error("Registration failed with status code: {}".format(response.status_code))
                logger.error("Response: {}".format(response.text))
        except requests.exceptions.RequestException as e:
            logger.error("Registration request failed: {}".format(str(e)))
        except Exception as e:
            logger.error("Registration error: {}".format(str(e)))
        # 注册到远程Agent后，开始心跳
        self._start_heartbeat(agent_heatbeat_url, self.register.get("proxy_client_name", "DefaultClientName") + "_" + self.name)  # 开始心跳

    def _start_heartbeat(self, agent_heatbeat_url, serverName):
        """
        启动心跳检测协程
        """
        import asyncio
        import threading
        def heartbeat():
            
            try:
                requests.post(
                    agent_heatbeat_url,
                    params={
                        "serverName": serverName
                    }
                )
            except Exception as e:
                logger.error("Heartbeat error: {}".format(str(e)))

        async def heartbeat_loop():
            while True:
                try:
                    heartbeat()
                    await asyncio.sleep(self.heartbeat_interval)
                except Exception as e:
                    logger.error("Heartbeat error: {}".format(str(e)))
                    await asyncio.sleep(5)

        def run_async_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self._heartbeat_task = loop.create_task(heartbeat_loop())
            loop.run_forever()

        thread = threading.Thread(target=run_async_loop, daemon=True)
        thread.start()
        logger.info("Heartbeat service started")

    def __del__(self):
        """
        确保在对象销毁时清理代理进程
        """
        if hasattr(self, '_proxy_process') and self._proxy_process:
            try:
                self._proxy_process.terminate()
                self._proxy_process.wait(timeout=5)  # 等待进程结束
            except Exception as e:
                logger.error("Error terminating proxy process: {}".format(e))

        if hasattr(self, '_heartbeat_task') and self._heartbeat_task:
            self._heartbeat_task.cancel()
