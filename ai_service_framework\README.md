# ai_service_framework

A unified Python package to bootstrap AI model services with RESTful endpoints using Flask. Easily install via pip and start serving your model.

## Installation
```bash
pip install ai-service-framework
```

## Package Version
```python
import ai_service_framework
print(ai_service_framework.__version__)
```

## Defining Your Service
```python
# my_service.py
from ai_service_framework.core import ModelService

class MyModelService(ModelService):
    def predict(self, input_data: dict) -> dict:
        # your inference logic here
        return {"output": ...}

    def health_check(self) -> dict:
        return {"status": "ok"}
```

## Running the Service
```bash
export SERVICE_CLASS=my_service.MyModelService
export HOST=0.0.0.0
export PORT=8000
ai-service-framework
```

## Development & Publishing
1. **Build**: `python -m build`
2. **Upload**: `twine upload dist/*`

---
*Licensed under the MIT License.*