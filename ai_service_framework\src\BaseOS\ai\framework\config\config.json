{"app": {"name": "flask-server", "name_zh": "样例服务", "host": "0.0.0.0", "port": 5000, "version": "1.0.0", "description": "A simple Flask server application"}, "register": {"proxy_client_name": "MGS53", "proxy_client_group": "MGS", "proxy_client_port": 8090, "proxy_server_ip": "*************", "proxy_server_port": 8090}, "provider": {"name": "mgs", "phone": "xxxxxxxxxxx", "email": "<EMAIL>"}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "filename": "app.log", "max_bytes": 10485760, "backup_count": 5}, "services": {"JsonService": {"name_zh": "json示例服务", "name": "JsonService", "input": {"input_json": "json"}, "parameters": {"conf": 0.4, "models": ["small"]}, "output": {"output_json": "json"}, "url": "/JsonService/customize", "type": "Json", "subtype": "Demo", "group": "BaseOS.API.Foundry"}}}