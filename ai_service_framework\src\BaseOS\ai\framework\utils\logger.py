import logging
import os
from logging.handlers import RotatingFileHandler
from typing import Optional

def setup_logger() -> logging.Logger:
    """
    根据配置创建全局日志对象
    
    Args:
        config (dict): 日志配置字典，包含level、format、filename等配置项
        
    Returns:
        logging.Logger: 配置好的日志对象
    """
    # 获取配置项，设置默认值
    log_level = 'INFO'
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    log_filename = 'service.log'
    max_bytes = 100 * 1024 * 1024
    backup_count = 5

    # 确保日志目录存在
    log_dir = os.path.dirname(log_filename)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

    # 创建logger对象
    logger = logging.getLogger('AI-Service')
    logger.setLevel(getattr(logging, log_level.upper()))

    # 创建文件处理器
    file_handler = RotatingFileHandler(
        filename=log_filename,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    
    # 设置日志格式
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 创建全局日志对象
_global_logger: Optional[logging.Logger] = None

def get_logger() -> logging.Logger:
    """
    获取全局日志对象
    
    Returns:
        logging.Logger: 全局日志对象
    """
    _init_logger()
    return _global_logger

def _init_logger() -> None:
    """
    初始化全局日志对象
    
    Args:
        config (dict): 日志配置
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = setup_logger()