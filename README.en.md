# AI-Docker

## Introduction
The AI-Docker project explores containerization solutions for integrating deep learning models into spatiotemporal computing platforms. It enables rapid application development and deployment through a unified application framework.

## Software Architecture
The project consists of two main components:

1. **AI Service Framework**
   - Unified Python service framework
   - RESTful API interface
   - Quick model service deployment
   - Standardized request-response format

2. **Dolphin Service**
   - Speech recognition service built on AI Service Framework
   - Supports audio file upload and real-time recording recognition
   - Web interface for online demonstration

## Installation Guide

### AI Service Framework Installation
```bash
pip install ai-service-framework
```

### Dolphin Service Deployment
1. Build Docker Image
```bash
cd Dolphin
docker build -t dolphin:v1.1 .
```

2. Run Container
```bash
docker run --rm -it --gpus all -v /path/to/Dolphin:/work -p 5000:5000 dolphin:v1.1
```

## Usage Guide

### Developing New Model Services
1. Inherit from ModelService Base Class
```python
from ai_service_framework.core import ModelService

class MyModelService(ModelService):
    def predict(self, request):
        # Implement prediction logic
        return {"result": "prediction"}

    def health_check(self):
        return {"status": "ok"}
```

2. Launch Service
```python
from ai_service_framework.app import create_app

app = create_app(MyModelService)
app.run(host='0.0.0.0', port=5000)
```

### Dolphin Service APIs
- `POST /predict`: Upload audio file for recognition
- `GET /health`: Health check
- `GET /metadata`: Get API metadata
- `GET /demo`: Access online demonstration page

## Development Guide

### Framework Development
```bash
cd ai_service_framework
python -m build
pip install dist/*.whl
```

### Docker Deployment
1. Prepare model files
2. Modify configuration files
3. Build image
4. Run container

## License
MIT License

## Contribution Guidelines
1. Fork the repository
2. Create feature branch
3. Commit your code
4. Create Pull Request
