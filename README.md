


# 1. 方案设计

## 1.1 整体架构

![AI应用系统设计](image/AI应用系统设计.png)

## 1.2 架构说明

- 平台级网关、集群级网关：集成网络代理和网关服务，实现集群内和集群间的服务代理与转发，统一流量管理、权限管理等功能
- 容器镜像服务：平台级容器镜像服务，各应用镜像统一管理平台，提供应用镜像的上传、拉取、共享等功能
- 应用市场管理服务：应用集成平台，提供应用的上传、发布和版本控制服务
- 应用服务器：基于BaseOS系统的服务节点，提供应用运行的基础软硬件环境
- AI应用：具备AI推理服务的应用，提供具体的API供外部调用

## 1.3 一键应用部署流程
![应用安装流程图](image/应用安装流程图.png)

## 1.4 应用开发流程

![应用开发流程](image/应用开发流程.png)

# 2. 框架使用指南及示例

## 2.1 引入AI应用开发框架包`ai_service_framework`
``` shell
git clone http://mgslab.openwebgis.com/baseos/ai/Framework.git
cd ai-docker/ai_service_framework
sudo chmod 777 build.sh
./build.sh 
pip install baseos_ai_framework-0.1.0-py3-none-any.whl
```

## 2.2 文件结构建议
```
ExampleProject/
    ├── Examples/
    │   ├── app.py                 # 应用入口         
    │   ├── services/              # 该服务下的功能合集
    │   │   ├── AService.py        # 服务实例，实现业务逻辑
    │   │   ├── __init__.py
    │   │   ├── JsonService.py
    │   │   └── TextService.py
    │   ├── config.json            # 该服务的配置管理
    │   └── start.sh               # 统一的服务启动脚本
    ├── run_as_docker.sh           # 统一的服务部署脚本，基于docker镜像方式
    └── run.sh                     # 统一的服务部署脚本，本地部署方式
```

## 2.2 本地启动flask服务验证
```shell
cd ExampleProject
bash run.sh            #本地验证
bash run_as_docker.sh  #拉取镜像，启动容器验证

:<<!
如果在配置文件中含"register"的注册信息且注册成功，
访问http://*************:60880/Gateway#/login，授权码：BaseOS2023，
在隧道管理的ai代理中可以找到注册信息。
!
```

## 2.4 配置文件config.json说明
```json
{
    "app": {
        "name": "flask-server",
        "env": "development",
        "debug": true,
        "host": "0.0.0.0", // 服务监听的ip地址，默认所有ipv4
        "port": 5000,      //服务监听的端口,默认5000
        "secret_key": "mgsdxsys",
        "version": "1.0.0",
        "description": "A simple Flask server application",
        "workers": 4,
        "timeout": 30,
        "keep_alive": 5,
        "max_requests": 0
    },
    "register": {                            //如果配置register，会向服务端注册，本地调试可以先省略
        "proxy_client_port": 8090,           //本地代理客户端监听端口，默认为8090
        "proxy_server_ip": "*************",  //远程代理服务端IP地址
        "proxy_server_port": 8090,           //远程代理服务端监听端口
        "agent_register_url": "",            //向Agent注册的url地址
        "agent_heatbeat_url": ""             //向Agent发送心跳连接的url地址
    },
    "database": {
        "url": "sqlite:///app.db",
        "pool_size": 5,
        "max_overflow": 10
    },
    "cache": {
        "type": "redis",
        "url": "redis://localhost:6379/0",
        "ttl": 300
    },
    "logging": {
        "level": "DEBUG",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "filename": "app.log",
        "max_bytes": 10485760,
        "backup_count": 5
    },
    "services": {
        "AService": {
            "name": "AService",     //和services中的类对应
            "input_type": "image",  //目前只支持image，audio,json和text格式
            "output_type": "image", 
            "url": "/AService/customize", //自定义url，如果没有默认'/name/predict'
            "type": "BaseOS.AI.Foundry",  //type命名需符合系统规范，如BaseOS.AI.Foundry为ai应用前缀
            "group": "image"              //所属组别
        },
        "JsonService": {
            "name": "JsonService",
            "input_type": "json",
            "output_type": "json",
            "type": "BaseOS.AI.Foundry",
            "group": "json"
        },
        "TextService": {
            "name": "TextService",
            "input_type": "text",
            "output_type": "text",
            "type": "BaseOS.AI.Foundry",
            "group": "text"
        }
    }
}
 ```

## 2.5 应用入口app.py
基于flask框架开发AI应用，获取基础app对象，编写应用入口文件（一般为main.py或app.py）
Server类对应MCPServer，可以看作服务/工具的合集，使用from BaseOS.ai.framework import Server调用。

``` python
# app.py
import os
from flask import render_template, request
from BaseOS.ai.framework import Server, ModelService

#自定义的services，推荐一个server的所有services放在同一个文件夹（services）下
from services import *

#路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_PATH = os.path.join(BASE_DIR, 'config.json')
UPLOAD_DIR = os.path.join(BASE_DIR, 'services', 'uploads') #存放上传下载文件
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

AServer = Server(CONFIG_PATH,UPLOAD_DIR)  #创建服务
AServer.create_services_from_config() #实例化各个services

#用于自定义app相关设置
app = AServer.app 
app.static_folder = os.path.join(BASE_DIR, 'static')  
#app.template_folder = os.path.join(BASE_DIR, 'template')   #用于自定义前端模板

#根据配置文件自动绑定路由
AServer.bind_sv_urls()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
```

## 2.6 服务/工具services

编写自己的service类（此处以json为例），Service类对应具体的服务/工具，使用from BaseOS.ai.framework import Service调用。
``` python
# JsonService.py
import os
from flask import jsonify, render_template
from BaseOS.ai.framework import ModelService, SVREGISTRY
from BaseOS.ai.framework.utils import get_logger
import json

logger = get_logger()

# 类名、注册名和配置文件中的service name需要对齐
@SVREGISTRY.register_module("JsonService")
class JsonService(ModelService):

    def __init__(self, config, upload_dir):
        """
        初始化服务对象
        :param config: server配置字典对象
        """
        super().__init__(config, upload_dir)

	# predict即算子的核心函数，对应config文件中配置的'url'，如果没有'url'则对应'/service_name/predict',
    def predict(self, request) -> dict:
        try:
        # 获取输入的JSON数据
            if request.is_json:
            # 直接从请求体获取JSON数据
                json_data = request.get_json()

            else:
                # 如果是表单提交的JSON字符串
                json_data = request.form.get('json')
            if not json_data:
                return jsonify({'error': 'No JSON data provided'}), 400
            # 尝试解析JSON字符串
            try:
                    if isinstance(json_data, str):
                        json_data = json.loads(json_data)
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid JSON format'}), 400

            # 获取后可以加入自己的处理

            return json_data

        except Exception as e:
            logger.error(f"Error processing JSON: {str(e)}")
            return jsonify({'error': str(e)}), 500


	#如果不使用默认前端模板，可以使用自定义模板，访问路径默认为'/service_name/home'
    def homepage(self):
        return render_template('base_index.html')

	#如果不使用默认功能，下方函数都可以自己编写

	#url为'/service_name/healthcheck'
    def health_check(self) -> dict:
        """
        检查服务的健康状态
        :return: 包含健康状态信息的字典
        """
        pass

	#url为'/service_name/metadata'
    def metadata(self) -> dict:
        """
        返回服务的元数据
        :return: 包含元数据信息的字典
        """
        pass
        
	#url为'/service_name/demo'
    def demo(self) -> str:
        """
        返回服务的演示页面或URL
        :return: 包含演示页面或URL的字符串
        """
        # render_template("demo.html")
        pass
```


## 2.7 服务验证前修改点

1. start.sh中的```CONFIG_FILE```变量，需要为config.json文件的相对路径
2. run.sh中的```CONFIG_FILE```变量，需要修改为run.sh所在文件夹路径```$WORK_DIR```后面拼接的相对路径，指向config.json文件
3. run.sh中的```### strat ###```注释后的 两个cd目录，第一个cd到项目所在的绝对路径，第二个cd到start.sh所在的相对路径
4. run_as_docker.sh中的修改点（仅针对镜像部署服务验证方式）
    1. 修改```SERVER_PORT```变量，表示本机被容器映射的端口
    2. 修改```IMAGE_NAME```变量，表示启动服务的基础镜像
    3. 修改```USERNAME```和```PASSWORD```变量，用来登录Harbor，下载基础镜像
    4. 修改```CONFIG_FILE```变量，表示config.json文件与```$WORK_DIR```的相对路径
    5. 修改```run_docker_container```函数中```docker run```命令最后的通过-v挂载的文件夹中```run.sh```脚本所在的容器内路径。